import { PayPalService } from "../services/paypalService.js";
import { TierService as TierServiceD1 } from "../services/tierServiceD1.js";
import { ResponseService } from "../services/responseService.js";
import { TokenService } from "../services/tokenService.js";

export class PurchaseController {
  constructor(env) {
    this.env = env;
    this.paypalService = new PayPalService(env);
    this.tierService = new TierServiceD1(env);
    this.responseService = new ResponseService();
  }

  async createPurchase(request) {
    try {
      // Extract email from JWT token
      const authHeader = request.headers.get("Authorization");
      const token = authHeader.split(" ")[1];
      const tokenService = new TokenService(this.env);
      const decoded = await tokenService.verifyToken(token);
      const email = decoded.email;

      console.log("User email from token:", email);

      const body = await request.json();
      const { tier, amount, addons = { addon1: false, addon2: false } } = body;

      if (!amount || typeof amount !== "number" || amount <= 0) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Valid amount is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Create one-time purchase order with specified amount
      const purchase = await this.paypalService.createOneTimePurchase(
        email,
        tier,
        addons,
        amount
      );

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            orderId: purchase.orderId,
            approvalUrl: purchase.approvalUrl,
            tier: purchase.tier,
            totalPrice: purchase.totalPrice,
            status: purchase.status,
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Purchase creation error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to create purchase",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async handlePurchaseSuccess(request) {
    try {
      const url = new URL(request.url);
      const orderId = url.searchParams.get("token");

      if (!orderId) {
        throw new Error("Missing order ID");
      }

      // Get purchase data from KV
      const purchaseData = await this.env.USERS_KV.get(
        `purchase:${orderId}`,
        "json"
      );

      if (!purchaseData) {
        throw new Error("No purchase found for this order");
      }

      // Capture the payment (handle case where already captured)
      try {
        await this.paypalService.capturePayment(orderId);
      } catch (error) {
        // If order is already captured, that's fine - continue with tier upgrade
        if (error.message.includes("ORDER_ALREADY_CAPTURED")) {
          console.log(
            `Order ${orderId} already captured, proceeding with tier upgrade`
          );
        } else {
          throw error; // Re-throw other errors
        }
      }

      // Update user's tier with lifetime access
      await this.tierService.upgradeEmailTier(
        purchaseData.email,
        purchaseData.tier,
        {
          purchaseId: orderId,
          addon1: purchaseData.addons?.addon1 || false,
          addon2: purchaseData.addons?.addon2 || false,
          isPermanent: true, // This indicates it's a lifetime purchase
          purchaseDate: new Date().toISOString(),
        }
      );

      // Clean up the temporary order data
      await this.env.USERS_KV.delete(`purchase:${orderId}`);

      // Redirect to frontend
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("purchase_status", "success");
      redirectUrl.searchParams.set("order_id", orderId);

      return Response.redirect(redirectUrl.toString(), 302);
    } catch (error) {
      console.error("Error handling purchase success:", error);
      throw error;
    }
  }

  async getOrderStatus(request) {
    try {
      const url = new URL(request.url);
      const orderId = url.searchParams.get("orderId");

      if (!orderId) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Order ID is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get order status from PayPal
      const orderStatus = await this.paypalService.getOrderStatus(orderId);

      return new Response(
        JSON.stringify({
          success: true,
          data: orderStatus,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting order status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to get order status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
