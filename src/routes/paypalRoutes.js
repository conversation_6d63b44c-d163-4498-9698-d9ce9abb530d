import { Router } from "itty-router";
import { SubscriptionController } from "../controllers/subscriptionController.js";
import { PurchaseController } from "../controllers/purchaseController.js";
import { WebhookController } from "../controllers/webhookController.js";
import { TokenService } from "../services/tokenService.js";

export function createPayPalRouter(env) {
  const router = Router({ base: "/api/paypal" });
  const subscriptionController = new SubscriptionController(env);
  const purchaseController = new PurchaseController(env);
  const webhookController = new WebhookController(env);

  // Token validation middleware
  async function validateToken(request) {
    const authHeader = request.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Unauthorized - Bearer token required",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const token = authHeader.split(" ")[1];
    if (!token) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Unauthorized - Invalid token format",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    try {
      const tokenService = new TokenService(env);
      await tokenService.verifyToken(token);
      return null; // Token is valid, continue
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Unauthorized - Invalid or expired token",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  // PayPal Subscription Routes - Protected
  router.post("/subscriptions", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return subscriptionController.createSubscription(request);
  });

  router.get("/subscriptions/success", async (request) => {
    try {
      const url = new URL(request.url);
      const baToken = url.searchParams.get("ba_token");
      console.log("PayPal success callback URL:", request.url);
      console.log("BA Token:", baToken);

      if (!baToken) {
        throw new Error("Missing billing agreement token");
      }

      // Get subscription data from KV using the ba_token
      const subscriptionData = await env.USERS_KV.get(
        `ba_token:${baToken}`,
        "json"
      );
      console.log("Found subscription data for token:", subscriptionData);

      if (!subscriptionData) {
        throw new Error("No subscription found for this token");
      }

      // Immediately upgrade the user's tier
      const tierService = subscriptionController.tierService;
      await tierService.updateUserTier(subscriptionData.userId, {
        tier: subscriptionData.tier,
        subscriptionId: subscriptionData.subscriptionId,
        addons: subscriptionData.addons || { addon1: false, addon2: false },
        status: "active",
        startDate: new Date().toISOString(),
      });

      // Clean up the temporary token mapping
      await env.USERS_KV.delete(`ba_token:${baToken}`);

      // Redirect to frontend with success status
      const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("subscription_status", "success");
      redirectUrl.searchParams.set(
        "subscription_id",
        subscriptionData.subscriptionId
      );

      return Response.redirect(redirectUrl.toString(), 302);
    } catch (error) {
      console.error("Error handling subscription success:", error);

      // Redirect to frontend with error status
      const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("subscription_status", "error");
      redirectUrl.searchParams.set("error", error.message);

      return Response.redirect(redirectUrl.toString(), 302);
    }
  });

  router.get("/subscriptions/cancel", (request) => {
    const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
    const redirectUrl = new URL("/dashboard", frontendUrl);
    redirectUrl.searchParams.set("subscription_status", "cancelled");
    return Response.redirect(redirectUrl.toString(), 302);
  });

  router.patch("/subscriptions/:subscriptionId/addons", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return subscriptionController.updateSubscriptionAddons(request);
  });

  router.get("/subscriptions/:subscriptionId/status", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return subscriptionController.getSubscriptionStatus(request);
  });

  // PayPal Purchase Routes - Protected
  router.post("/purchases", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return purchaseController.createPurchase(request);
  });

  router.get("/purchases/success", (request) =>
    purchaseController.handlePurchaseSuccess(request)
  );

  router.get("/purchases/status", (request) =>
    purchaseController.getOrderStatus(request)
  );

  // PayPal Webhook Routes
  router.post("/webhooks", (request) =>
    webhookController.handlePayPalWebhook(request)
  );

  // Add error handling for unmatched routes
  router.all("*", () => {
    console.log("PayPal Router: Route not found");
    return new Response(
      JSON.stringify({
        success: false,
        error: "Endpoint not found",
      }),
      {
        status: 404,
        headers: { "Content-Type": "application/json" },
      }
    );
  });

  return router;
}
