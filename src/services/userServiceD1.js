// src/services/userServiceD1.js
// Updated UserService to use D1 database instead of KV storage

import { EmailService } from "./emailService.js";
import { DatabaseService } from "./databaseService.js";
import { v4 as uuidv4 } from 'uuid';

export class UserService {
  constructor(env) {
    this.env = env;
    this.db = new DatabaseService(env);
    this.emailService = new EmailService(env);
  }

  async registerUser(email, password, domain) {
    try {
      console.log(`🔄 Starting user registration for: ${email}`);

      // Check if domain already exists
      const existingDomain = await this.db.getDomainByName(domain);
      if (existingDomain) {
        throw new Error("Domain already registered");
      }

      // Check if user already exists
      const existingUser = await this.db.getUserByEmail(email);
      if (existingUser) {
        throw new Error("Email already registered");
      }

      // Generate user ID and API key
      const userId = uuidv4();
      const apiKey = uuidv4();
      const domainId = uuidv4();
      const now = new Date().toISOString();

      // Create user
      const userData = {
        id: userId,
        email: email,
        password: password, // Should be hashed in production
        type: 'api',
        status: 'active',
        created_at: now,
        updated_at: now
      };

      await this.db.createUser(userData);

      // Create domain
      const domainData = {
        id: domainId,
        user_id: userId,
        domain: domain,
        api_key: apiKey,
        status: 'active',
        tier: 'free',
        created_at: now,
        activated_at: now
      };

      await this.db.createDomain(domainData);

      // Create API key mapping
      await this.db.createApiKeyMapping(apiKey, userId, domainId);

      console.log(`✅ User registered successfully: ${email}`);

      return {
        id: userId,
        email: email,
        domains: [domainData],
        created_at: now
      };

    } catch (error) {
      console.error("Error registering user:", error);
      throw error;
    }
  }

  async addDomain(email, domain) {
    try {
      console.log(`🔄 Adding domain ${domain} for user: ${email}`);

      // Check if domain already exists
      const existingDomain = await this.db.getDomainByName(domain);
      if (existingDomain) {
        throw new Error("Domain already registered");
      }

      // Get user
      const user = await this.db.getUserByEmail(email);
      if (!user) {
        throw new Error("User not found");
      }

      // Generate new API key and domain ID
      const apiKey = uuidv4();
      const domainId = uuidv4();
      const now = new Date().toISOString();

      // Create domain
      const domainData = {
        id: domainId,
        user_id: user.id,
        domain: domain,
        api_key: apiKey,
        status: 'active',
        tier: 'free',
        created_at: now,
        activated_at: now
      };

      await this.db.createDomain(domainData);

      // Create API key mapping
      await this.db.createApiKeyMapping(apiKey, user.id, domainId);

      // Get updated user with all domains
      const updatedUser = await this.db.getUserWithDomains(user.id);

      console.log(`✅ Domain added successfully: ${domain}`);

      return updatedUser;

    } catch (error) {
      console.error("Error adding domain:", error);
      throw error;
    }
  }

  async getUserByApiKey(apiKey) {
    try {
      // Get user ID from API key
      const userId = await this.db.getUserIdByApiKey(apiKey);
      if (!userId) {
        return null;
      }

      // Get user with domains
      const user = await this.db.getUserWithDomains(userId);
      return user;

    } catch (error) {
      console.error("Error getting user by API key:", error);
      throw error;
    }
  }

  async getUserByEmail(email) {
    try {
      const user = await this.db.getUserByEmail(email);
      if (!user) {
        return null;
      }

      // Get user with domains
      const userWithDomains = await this.db.getUserWithDomains(user.id);
      return userWithDomains;

    } catch (error) {
      console.error("Error getting user by email:", error);
      throw error;
    }
  }

  async updateUserTier(userId, tier, apiKey = null) {
    try {
      console.log(`🔄 Updating user tier for ${userId} to ${tier}`);

      const user = await this.db.getUserById(userId);
      if (!user) {
        throw new Error("User not found");
      }

      // Update user tier
      await this.db.setUserTier(userId, tier);

      // If API key is provided, update the specific domain tier
      if (apiKey) {
        const domains = await this.db.getDomainsByUserId(userId);
        const domain = domains.find(d => d.api_key === apiKey);
        
        if (domain) {
          await this.db.updateDomain(domain.id, {
            status: domain.status,
            tier: tier,
            activated_at: domain.activated_at
          });
        }
      } else {
        // Update all domains for this user
        const domains = await this.db.getDomainsByUserId(userId);
        for (const domain of domains) {
          await this.db.updateDomain(domain.id, {
            status: domain.status,
            tier: tier,
            activated_at: domain.activated_at
          });
        }
      }

      console.log(`✅ User tier updated successfully: ${userId} -> ${tier}`);

      return await this.db.getUserWithDomains(userId);

    } catch (error) {
      console.error("Error updating user tier:", error);
      throw error;
    }
  }

  async validateLicense(license) {
    try {
      // For backward compatibility, treat license as API key
      return await this.getUserByApiKey(license);
    } catch (error) {
      console.error("Error validating license:", error);
      throw error;
    }
  }

  async getUserQuotaInfo(userId) {
    try {
      const user = await this.db.getUserById(userId);
      if (!user) {
        throw new Error("User not found");
      }

      // Get user tier
      const userTier = await this.db.getUserTier(userId);
      const tier = userTier?.tier || 'free';

      // Get tier settings
      const tierSettings = await this.db.getTierSettings();
      const tierConfig = tierSettings?.config?.[tier];

      if (!tierConfig) {
        throw new Error(`Tier configuration not found for: ${tier}`);
      }

      // Get quota usage for all types
      const imageUsage = await this.db.getQuotaUsage(userId, 'images') || { count: 0 };
      const contentUsage = await this.db.getQuotaUsage(userId, 'content') || { count: 0 };
      const titleUsage = await this.db.getQuotaUsage(userId, 'title') || { count: 0 };

      return {
        tier: tier,
        quota: {
          resetDate: imageUsage.reset_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          usage: {
            images: imageUsage.count,
            content: contentUsage.count,
            title: titleUsage.count
          },
          limits: {
            images: tierConfig.imagesQuota || tierConfig.maxQuota,
            content: tierConfig.contentQuota || tierConfig.maxQuota,
            title: tierConfig.titleQuota || tierConfig.maxQuota
          }
        }
      };

    } catch (error) {
      console.error("Error getting user quota info:", error);
      throw error;
    }
  }

  async removeDomain(email, domain) {
    try {
      console.log(`🔄 Removing domain ${domain} for user: ${email}`);

      const user = await this.db.getUserByEmail(email);
      if (!user) {
        throw new Error("User not found");
      }

      const domainRecord = await this.db.getDomainByName(domain);
      if (!domainRecord || domainRecord.user_id !== user.id) {
        throw new Error("Domain not found or doesn't belong to user");
      }

      // Delete API key mapping
      await this.db.deleteApiKeyMapping(domainRecord.api_key);

      // Delete domain
      await this.db.deleteDomain(domainRecord.id);

      console.log(`✅ Domain removed successfully: ${domain}`);

      return await this.db.getUserWithDomains(user.id);

    } catch (error) {
      console.error("Error removing domain:", error);
      throw error;
    }
  }

  async createPortalUser(email, password) {
    try {
      console.log(`🔄 Creating portal user: ${email}`);

      const normalizedEmail = email.toLowerCase().trim();

      // Check if user already exists
      const existingUser = await this.db.getUserByEmail(normalizedEmail);
      if (existingUser) {
        throw new Error("Email already registered");
      }

      const userId = uuidv4();
      const now = new Date().toISOString();

      // Create user
      const userData = {
        id: userId,
        email: normalizedEmail,
        password: null, // Portal users don't have password in main user table
        type: 'portal',
        status: 'active',
        created_at: now,
        updated_at: now
      };

      await this.db.createUser(userData);

      // Store portal credentials
      const credentialsData = {
        plain_password: password, // Store plain password for portal users if needed
        hashed_password: password, // Should be properly hashed in production
        created_at: now
      };

      await this.db.createPortalCredentials(userId, credentialsData);

      console.log("✅ Portal user created successfully:", normalizedEmail);

      return {
        id: userId,
        email: normalizedEmail,
        type: "portal",
        status: "active",
        createdAt: now
      };

    } catch (error) {
      console.error("Error creating portal user:", error);
      throw error;
    }
  }
}