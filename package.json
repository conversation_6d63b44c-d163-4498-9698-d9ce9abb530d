{"name": "your-project", "version": "1.0.0", "type": "module", "scripts": {"dev": "wrangler dev --port 3000 --test-scheduled", "dev:alt": "wrangler dev --port 4000", "dev:https": "wrangler dev --port 3000 --local-protocol https", "dev:local": "wrangler dev --port 3000 --ip 127.0.0.1", "dev:network": "wrangler dev --port 3000 --ip 0.0.0.0", "dev:debug": "wrangler dev --port 3000 --inspector-port 9229", "deploy": "wrangler deploy", "setup-d1": "./setup-d1.sh", "switch-to-d1": "node switch-to-d1.js switch", "revert-to-kv": "node switch-to-d1.js revert", "migrate": "curl -X POST http://localhost:3000/api/migration/run", "migration-status": "curl -X GET http://localhost:3000/api/migration/status"}, "dependencies": {"itty-router": "^3.0.12", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "qrcode": "^1.5.4", "uuid": "^9.0.1", "xendit-node": "^6.1.0"}, "devDependencies": {"wrangler": "^3.28.1"}}